import { SendOutlined, UserOutlined } from "@ant-design/icons";
import { PageContainer } from "@ant-design/pro-components";
import { Avatar, Button, Input, Space } from "antd";
import React, { useEffect, useRef, useState } from "react";
import avatar from "../../../public/avatar.svg";
import c from "./index.module.less";

interface Message {
  id: string;
  content: string;
  sender: "user" | "assistant";
  timestamp: Date;
}

const Chat: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      content: "我是招聘智能小助手，请问有什么可以帮您？",
      sender: "assistant",
      timestamp: new Date("2024-01-01T09:44:00"),
    },
  ]);
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      content: inputValue.trim(),
      sender: "user",
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, newMessage]);
    setInputValue("");
    setIsLoading(true);

    setTimeout(() => {
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: "感谢您的提问！我会尽力为您提供帮助。",
        sender: "assistant",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, assistantMessage]);
      setIsLoading(false);
    }, 1000);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("zh-CN", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    });
  };

  return (
    <PageContainer>
      <div className={c.chatContainer}>
        <div className={c.chatWrapper}>
          {/* Chat Header */}
          {/* <div className={c.chatHeader}>
            <Flex align="center" gap={12}>
              <Avatar size={40} src={avatar}></Avatar>
              <div>
                <div className={c.headerTitle}>汇川技术 招聘智能小助手</div>
                <div className={c.headerTime}>09:44</div>
              </div>
            </Flex>
          </div> */}

          {/* Messages Area */}
          <div className={c.messagesArea}>
            {messages.map((message) => (
              <div
                key={message.id}
                className={`${c.messageItem} ${
                  message.sender === "user" ? c.userMessage : c.assistantMessage
                }`}
              >
                <div className={c.messageContent}>
                  <div className={c.messageAvatar}>
                    {message.sender === "assistant" ? (
                      <Avatar size={40} src={avatar}></Avatar>
                    ) : (
                      <Avatar size={32} icon={<UserOutlined />} />
                    )}
                  </div>
                  <div className={c.messageBubble}>
                    <div className={c.messageText}>{message.content}</div>
                    <div className={c.messageTime}>
                      {formatTime(message.timestamp)}
                    </div>
                  </div>
                </div>
              </div>
            ))}
            {isLoading && (
              <div className={`${c.messageItem} ${c.assistantMessage}`}>
                <div className={c.messageContent}>
                  <div className={c.messageAvatar}>
                    <Avatar
                      size={32}
                      style={{
                        backgroundColor: "#1890ff",
                        fontSize: "12px",
                        fontWeight: "bold",
                      }}
                    >
                      CATL
                    </Avatar>
                  </div>
                  <div className={c.messageBubble}>
                    <div className={c.messageText}>正在输入...</div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          <div className={c.inputArea}>
            <Space.Compact style={{ width: "100%" }}>
              <Input.TextArea
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="请输入..."
                autoSize={{ minRows: 1, maxRows: 4 }}
                style={{
                  resize: "none",
                  borderRadius: "8px 0 0 8px",
                }}
                disabled={isLoading}
              />
              <Button
                type="primary"
                icon={<SendOutlined />}
                onClick={handleSendMessage}
                loading={isLoading}
                style={{
                  height: "auto",
                  borderRadius: "0 8px 8px 0",
                  minHeight: "32px",
                }}
                disabled={!inputValue.trim()}
              />
            </Space.Compact>
          </div>
        </div>
        <div className={c.chatOptions}>2</div>
      </div>
    </PageContainer>
  );
};

export default Chat;
