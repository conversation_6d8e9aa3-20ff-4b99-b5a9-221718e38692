.chatContainer {
  display: flex;
  align-items: stretch;
  justify-content: center;
  height: 75vh;
  gap: 16px;
  background: #f5f5f5;
  border-radius: 16px;
  padding: 16px;
}

.chatWrapper {
  flex: 1;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chatOptions {
  width: 30%;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
}

/* Chat Header */
.chatHeader {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #ffffff;
}

.headerTitle {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  line-height: 1.4;
}

.headerTime {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 2px;
}

/* Messages Area */
.messagesArea {
  flex: 1;
  padding: 16px 20px;
  overflow-y: auto;
  background: #fafafa;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.messageItem {
  display: flex;
  width: 100%;
}

.assistantMessage {
  justify-content: flex-start;
}

.userMessage {
  justify-content: flex-end;
}

.messageContent {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  max-width: 70%;
}

.userMessage .messageContent {
  flex-direction: row-reverse;
}

.messageAvatar {
  flex-shrink: 0;
}

.messageBubble {
  background: #ffffff;
  border-radius: 12px;
  padding: 12px 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
}

.userMessage .messageBubble {
  background: #1890ff;
  color: #ffffff;
}

.messageText {
  font-size: 14px;
  line-height: 1.5;
  word-wrap: break-word;
  margin-bottom: 4px;
}

.messageTime {
  font-size: 11px;
  opacity: 0.7;
  text-align: right;
}

.userMessage .messageTime {
  color: rgba(255, 255, 255, 0.8);
}

/* Input Area */
.inputArea {
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  background: #ffffff;
}

/* Scrollbar Styling */
.messagesArea::-webkit-scrollbar {
  width: 6px;
}

.messagesArea::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.messagesArea::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.messagesArea::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

@media screen and (max-width: 768px) {
  .chatContainer {
    flex-direction: column;
    height: 85vh;
    padding: 8px;
    gap: 8px;
  }

  .chatWrapper {
    width: 100%;
    flex: 1;
  }

  .chatOptions {
    display: none;
  }

  .chatHeader {
    padding: 12px 16px;
  }

  .messagesArea {
    padding: 12px 16px;
  }

  .inputArea {
    padding: 12px 16px;
  }

  .messageContent {
    max-width: 85%;
  }

  .headerTitle {
    font-size: 14px;
  }
}
